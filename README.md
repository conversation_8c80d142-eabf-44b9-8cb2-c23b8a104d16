# AM Monitor

Data Pipeline Monitoring & Operations Dashboard - Hệ thống web nội bộ để giám sát và vận hành data pipeline giữa các microservices.

## Cấu trúc Project

```
implements/
├── cmd/
│   └── web/
│       └── main.go                 # Entry point của ứng dụng
├── internal/
│   ├── config/
│   │   └── config.go              # Configuration management
│   ├── models/
│   │   └── response.go            # Response models theo giao diện
│   ├── handlers/
│   │   ├── api_handler.go         # API endpoints với mock data
│   │   └── web_handler.go         # Web page handlers
│   └── server/
│       └── server.go              # Server setup và routes
├── web/
│   ├── templates/
│   │   ├── layouts/
│   │   │   └── base.html          # Base HTML layout
│   │   ├── dashboard.html         # Màn 1 - Dashboard chính
│   │   ├── error_logs.html        # Màn 2 - Error logs
│   │   └── session_detail.html    # Màn 3 - Session detail
│   └── static/
│       ├── css/
│       │   └── style.css          # Custom styling
│       └── js/
│           └── app.js             # Frontend JavaScript
├── go.mod                         # Go module dependencies
├── .env                          # Environment variables
└── README.md                     # Documentation
```

## Cài đặt và Chạy

### 1. Cài đặt dependencies:
```bash
cd implements
go mod tidy
```

### 2. Cấu hình môi trường (file .env):
```env
KAFKA_BROKER=localhost:9092
MONGODB_URI=mongodb://localhost:27017/am_monitor
STARROCK_URI=starrocks://localhost:9030/analytics
SERVER_PORT=8080
```

### 3. Chạy ứng dụng:
```bash
go run cmd/web/main.go
```

### 4. Truy cập ứng dụng:
- Dashboard: http://localhost:8080/
- Error logs: http://localhost:8080/errors
- Health check: http://localhost:8080/health

## Tính năng

### 3 Giao diện chính:

#### Màn 1 - Dashboard (/)
- Form filter sidebar (ConnectorID, SessionID, thông tin connector)
- Bảng sessions với các columns: ID, SessionID, Thời gian bắt đầu/kết thúc, Consumer/Process Status, Mode, Action
- Button "Log Sync AM -> Orches" chuyển đến màn 2
- Button "Chi tiết" cho từng session chuyển đến màn 3
- Pagination cho danh sách sessions

#### Màn 2 - Error Logs (/errors)
- Hiển thị danh sách request lỗi
- Bảng với columns: Loại, Action Time, Error Message, CURL
- Button "Copy" để copy CURL command
- Cùng sidebar filter như màn 1

#### Màn 3 - Session Detail (/sessions/{session_id})
- Hiển thị thông tin chi tiết của session
- 3 buttons thống kê: Consumer Count, Process Count, Error Count
- Bảng Kafka Topics với Tên Topic và Lag
- Cùng sidebar filter như màn 1

## API Endpoints

### Web Pages
- `GET /` → Dashboard (màn 1)
- `GET /errors` → Error logs (màn 2)  
- `GET /sessions/{session_id}` → Session detail (màn 3)

### API Endpoints
- `GET /api/sessions?connector_id=&session_id=&page=1&limit=10` → Get sessions với filters
- `GET /api/sessions/{session_id}/errors` → Get error logs của session
- `GET /api/sessions/{session_id}/detail` → Get session detail với kafka topics
- `POST /api/sessions/sync-trigger` → Trigger sync operation
- `GET /health` → Health check

## Data Models

### SessionResponse
```go
type SessionResponse struct {
    ID             int    `json:"id"`
    SessionID      string `json:"session_id"`
    StartTime      string `json:"start_time"`
    EndTime        string `json:"end_time"`
    ConsumerStatus string `json:"consumer_status"`
    ProcessStatus  string `json:"process_status"`
    Mode           string `json:"mode"`
}
```

### ErrorLogResponse  
```go
type ErrorLogResponse struct {
    Type         string `json:"type"`
    ActionTime   string `json:"action_time"`
    ErrorMessage string `json:"error_message"`
    CURL         string `json:"curl"`
}
```

### SessionDetailResponse
```go
type SessionDetailResponse struct {
    SessionInfo   SessionResponse      `json:"session_info"`
    ConsumerCount int                  `json:"consumer_count"`
    ProcessCount  int                  `json:"process_count"`
    ErrorCount    int                  `json:"error_count"`
    KafkaTopics   []KafkaTopicResponse `json:"kafka_topics"`
}
```

## Customization

### Thay thế Mock Data
Để connect với data thực tế, chỉ cần thay thế logic trong các API handlers:

1. `internal/handlers/api_handler.go` - Thay thế mock data bằng calls đến MongoDB/StarRocks/Kafka
2. Implement các service layers để xử lý business logic
3. Implement repository patterns để access data sources

### Thêm tính năng mới
1. Thêm routes mới trong `internal/server/server.go`
2. Implement handlers trong `internal/handlers/`
3. Tạo templates mới trong `web/templates/`
4. Update models trong `internal/models/` nếu cần

## Tech Stack

- **Backend**: Go 1.21+ với Gin Web Framework
- **Frontend**: HTML5, Bootstrap 5, Vanilla JavaScript  
- **Configuration**: Environment variables với godotenv
- **Templates**: Go HTML templates
- **Styling**: Bootstrap 5 + Custom CSS

## Development

Project này được thiết kế để dễ dàng customize và mở rộng. Tất cả API endpoints đã được định nghĩa và return đúng format data theo giao diện, bạn chỉ cần thay thế mock data bằng implementation thực tế.