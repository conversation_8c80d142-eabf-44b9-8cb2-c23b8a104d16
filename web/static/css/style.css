/* Additional custom styles */
.sidebar {
    position: sticky;
    top: 20px;
    height: fit-content;
}

.main-content {
    min-height: 600px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.8em;
    padding: 0.35em 0.65em;
}

.btn-action {
    border-radius: 20px;
}

.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        margin-bottom: 20px;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .table-responsive {
        border: none;
    }
}

/* Loading spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Status indicators */
.status-finished {
    color: #28a745;
}

.status-running, .status-processing {
    color: #ffc107;
}

.status-failed, .status-error {
    color: #dc3545;
}

/* Hover effects */
.table tbody tr:hover {
    background-color: rgba(0,123,255,.075);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transition: all 0.2s ease-in-out;
}

/* Custom scrollbar */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #555;
}