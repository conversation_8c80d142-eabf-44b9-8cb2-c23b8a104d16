// Global JavaScript utilities for AM Monitor

// Utility functions
function showLoading(buttonElement) {
    const originalText = buttonElement.innerHTML;
    buttonElement.innerHTML = '<span class="loading"></span> Loading...';
    buttonElement.disabled = true;
    
    return function hideLoading() {
        buttonElement.innerHTML = originalText;
        buttonElement.disabled = false;
    };
}

function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.minWidth = '300px';
    
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    try {
        const date = new Date(dateString);
        return date.toLocaleString('vi-VN');
    } catch (error) {
        return dateString;
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// API call wrapper with error handling
async function apiCall(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        showAlert(`API call failed: ${error.message}`, 'danger');
        throw error;
    }
}

// Session management utilities
class SessionManager {
    constructor() {
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filters = {};
    }
    
    setFilter(key, value) {
        if (value) {
            this.filters[key] = value;
        } else {
            delete this.filters[key];
        }
        this.currentPage = 1; // Reset to first page when filtering
    }
    
    getQueryString() {
        const params = new URLSearchParams({
            page: this.currentPage,
            limit: this.itemsPerPage,
            ...this.filters
        });
        return params.toString();
    }
    
    async loadSessions() {
        try {
            const data = await apiCall(`/api/sessions?${this.getQueryString()}`);
            this.renderSessions(data.sessions);
            this.renderPagination(data.total);
            return data;
        } catch (error) {
            console.error('Failed to load sessions:', error);
        }
    }
    
    renderSessions(sessions) {
        // This will be overridden by specific page implementations
        console.log('Sessions loaded:', sessions);
    }
    
    renderPagination(total) {
        // This will be overridden by specific page implementations
        console.log('Total sessions:', total);
    }
}

// Global session manager instance
window.sessionManager = new SessionManager();

// Copy to clipboard utility
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showAlert('Copied to clipboard!', 'success');
        return true;
    } catch (err) {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            showAlert('Copied to clipboard!', 'success');
            return true;
        } catch (fallbackErr) {
            showAlert('Failed to copy to clipboard', 'danger');
            return false;
        } finally {
            document.body.removeChild(textArea);
        }
    }
}

// Form utilities
function validateForm(formElement) {
    const inputs = formElement.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

function resetForm(formElement) {
    formElement.reset();
    const invalidInputs = formElement.querySelectorAll('.is-invalid');
    invalidInputs.forEach(input => input.classList.remove('is-invalid'));
}

// Initialize common functionality when DOM loads
document.addEventListener('DOMContentLoaded', function() {
    // Add loading states to buttons with data-loading attribute
    const loadingButtons = document.querySelectorAll('[data-loading]');
    loadingButtons.forEach(button => {
        button.addEventListener('click', function() {
            const hideLoading = showLoading(this);
            // Auto-hide loading after 5 seconds as fallback
            setTimeout(hideLoading, 5000);
        });
    });
    
    // Auto-dismiss alerts
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    });
    
    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Export utilities for use in other scripts
window.AMMonitor = {
    showLoading,
    showAlert,
    formatDateTime,
    debounce,
    apiCall,
    copyToClipboard,
    validateForm,
    resetForm,
    SessionManager
};