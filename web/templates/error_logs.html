<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Logs - AM Monitor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .main-content {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
        .btn-action {
            padding: 4px 12px;
            font-size: 0.875rem;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #28a745;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar mb-4">
                    <h5 class="mb-3">Thông tin</h5>
                    <form>
                        <div class="mb-3">
                            <label for="connectorId" class="form-label">ConnectorID</label>
                            <input type="text" class="form-control" id="connectorId" name="connector_id">
                        </div>
                        <div class="mb-3">
                            <label for="sessionId" class="form-label">SessionID</label>
                            <input type="text" class="form-control" id="sessionId" name="session_id">
                        </div>
                        <button type="button" class="btn btn-primary" onclick="loadErrorLogs()">Monitor</button>
                    </form>
                </div>

                <div class="sidebar">
                    <h5 class="mb-3">Thông tin chi tiết Connector</h5>
                    <div class="mb-3">
                        <label for="connectorName" class="form-label">Tên:</label>
                        <input type="text" class="form-control" id="connectorName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Trạng thái kết nối:</label>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="mb-3">
                        <label for="connectorStatus" class="form-label">Trạng thái đồng bộ:</label>
                        <input type="text" class="form-control" id="connectorStatus" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="jobName" class="form-label">Loại Connector:</label>
                        <input type="text" class="form-control" id="jobName" readonly>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="button" class="btn btn-success w-100" onclick="goToDashboard()">
                        Log Sync AM -> Orches
                    </button>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="main-content">
                    <h4 class="mb-4" id="errorLogsTitle">Danh sách request lỗi khi sync data từ Marketplace sang Orchestration</h4>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="errorLogsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>Loại</th>
                                    <th>Action Time</th>
                                    <th>Error Message</th>
                                    <th>CURL</th>
                                </tr>
                            </thead>
                            <tbody id="errorLogsTableBody">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
    <script>
    // Load error logs when page loads
    document.addEventListener('DOMContentLoaded', function() {
        loadErrorLogs();
    });

    function loadErrorLogs() {
        // Get session_id from URL or default
        const sessionId = '250910053049147'; // Default session for demo
        
        fetch(`/api/sessions/${sessionId}/errors`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('errorLogsTitle').textContent = data.title;
                renderErrorLogsTable(data.error_logs);
            })
            .catch(error => {
                console.error('Error loading error logs:', error);
            });
    }

    function renderErrorLogsTable(errorLogs) {
        const tbody = document.getElementById('errorLogsTableBody');
        tbody.innerHTML = '';
        
        errorLogs.forEach(error => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${error.type}</td>
                <td>${error.action_time}</td>
                <td>
                    <div class="text-truncate" style="max-width: 300px;" title="${error.error_message}">
                        ${error.error_message}
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <code class="text-truncate me-2" style="max-width: 200px;" title="${error.curl}">
                            ${error.curl}
                        </code>
                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="copyCurl('${error.curl}')">
                            Copy
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    function copyCurl(curlCommand) {
        navigator.clipboard.writeText(curlCommand).then(function() {
            // Show temporary success message
            const originalText = event.target.textContent;
            event.target.textContent = 'Copied!';
            event.target.classList.add('btn-success');
            event.target.classList.remove('btn-outline-primary');
            
            setTimeout(() => {
                event.target.textContent = originalText;
                event.target.classList.remove('btn-success');
                event.target.classList.add('btn-outline-primary');
            }, 2000);
        }, function(err) {
            console.error('Could not copy text: ', err);
            alert('Failed to copy CURL command');
        });
    }

    function goToDashboard() {
        window.location.href = '/';
    }
    </script>
</body>
</html>