<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AM Monitor Dashboard</title>
    <style>
        /* General Styles */
        :root {
            --border-color: #dcdcdc;
            --background-color: #f5f5f5;
            --sidebar-bg: #ffffff;
            --header-bg: #f9f9f9;
            --button-bg: #e0e0e0;
            --button-hover-bg: #d0d0d0;
            --toggle-bg-off: #ccc;
            --toggle-bg-on: #4CAF50;
            --font-family: Arial, sans-serif;
        }

        body {
            font-family: var(--font-family);
            margin: 0;
            background-color: var(--background-color);
            color: #333;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 300px;
            padding: 20px;
            background-color: var(--sidebar-bg);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-section {
            border: 1px solid var(--border-color);
            padding: 15px;
            border-radius: 5px;
        }

        .form-section h3 {
            margin-top: 0;
            font-size: 16px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: bold;
        }

        .form-group input[type="text"] {
            width: calc(100% - 16px);
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }
        
        .monitor-button {
            width: 100px;
            padding: 10px;
            border: 1px solid var(--border-color);
            background-color: var(--button-bg);
            cursor: pointer;
            border-radius: 4px;
            font-weight: bold;
            display: block;
            margin: 10px auto;
        }

        .monitor-button:hover {
            background-color: var(--button-hover-bg);
        }
        
        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--toggle-bg-off);
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--toggle-bg-on);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .log-sync-button {
            width: 100%;
            padding: 12px;
            background-color: var(--button-bg);
            border: 1px solid var(--border-color);
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-top: auto; /* Pushes the button to the bottom */
        }
         .log-sync-button:hover {
            background-color: var(--button-hover-bg);
        }

        /* Main Content */
        .main-content {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .main-content h2 {
           margin-top: 0;
        }

        /* Table Styles */
        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
        }

        th, td {
            border: 1px solid var(--border-color);
            padding: 12px;
            text-align: left;
            font-size: 14px;
        }

        thead {
            background-color: var(--header-bg);
        }
        
        th {
            font-weight: bold;
        }

        .action-button {
            padding: 5px 10px;
            border: 1px solid var(--border-color);
            background: none;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .action-button:hover {
            background-color: #f0f0f0;
        }
        
        .copy-button {
            padding: 5px 10px;
            border: 1px solid var(--border-color);
            background: none;
            cursor: pointer;
            border-radius: 4px;
        }
        .copy-button:hover {
            background-color: #f0f0f0;
        }

        /* Modal Styles */
        .modal {
            display: none; /* Hidden by default */
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5); /* Dim background */
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #fefefe;
            margin: auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 700px;
            border-radius: 8px;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
        }
        
        .modal-content h3 {
            margin-top: 0;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        .summary-boxes {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }

        .summary-box {
            flex: 1;
            border: 1px solid var(--border-color);
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .summary-box p {
            margin: 0;
            font-size: 14px;
        }

        /* Status badges */
        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            border-radius: 4px;
            color: white;
            text-transform: uppercase;
        }
        
        .badge.finished {
            background-color: #28a745;
        }
        
        .badge.running, .badge.processing {
            background-color: #ffc107;
            color: #212529;
        }
        
        .badge.failed, .badge.error {
            background-color: #dc3545;
        }

        /* Loading spinner */
        .loading {
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

    </style>
</head>
<body>

    <div class="container">
        <aside class="sidebar">
            <div class="form-section">
                <h3>Thông tin</h3>
                <div class="form-group">
                    <label for="connector-id">ConnectorID</label>
                    <input type="text" id="connector-id">
                </div>
                <div class="form-group">
                    <label for="session-id">SessionID</label>
                    <input type="text" id="session-id">
                </div>
                <button class="monitor-button" onclick="loadSessions()">Monitor</button>
            </div>

            <div class="form-section">
                <h3>Thông tin chi tiết Connector</h3>
                <div class="form-group">
                    <label for="connector-name">Tên</label>
                    <input type="text" id="connector-name">
                </div>
                <div class="form-group">
                    <label>Trạng thái kết nối</label>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                <div class="form-group">
                    <label for="sync-status">Trạng thái đồng bộ</label>
                    <input type="text" id="sync-status">
                </div>
                <div class="form-group">
                    <label for="connector-type">Loại Connector</label>
                    <input type="text" id="connector-type">
                </div>
                 <div class="form-group">
                    <label for="report-job">Job tổng hợp báo cáo</label>
                    <input type="text" id="report-job" value="orchestration-data-pipeline-report" readonly>
                </div>
            </div>

            <button class="log-sync-button" id="log-sync-btn">Log Sync All -> Orches</button>
        </aside>

        <main class="main-content">
            <div id="session-list-view">
                <h2>Sessions</h2>
                <div id="sessions-loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Loading sessions...</p>
                </div>
                <table id="sessions-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>SessionID</th>
                            <th>Thời gian bắt đầu</th>
                            <th>Thời gian kết thúc</th>
                            <th>Consume Status</th>
                            <th>Trạng thái Process</th>
                            <th>Mode</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody id="sessions-table-body">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
            </div>

            <div id="error-log-view" style="display: none;">
                <h2>Danh sách request lỗi khi sync data từ Marketplace sang Orchestration</h2>
                <div id="errors-loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p>Loading error logs...</p>
                </div>
                <table id="errors-table">
                    <thead>
                        <tr>
                            <th>Loại</th>
                            <th>Action Time</th>
                            <th>Error Message</th>
                            <th>cURL</th>
                        </tr>
                    </thead>
                    <tbody id="errors-table-body">
                        <!-- Error data will be loaded here -->
                    </tbody>
                </table>
                <button class="monitor-button" onclick="goBackToSessions()" style="margin-top: 20px;">Back to Sessions</button>
            </div>
        </main>
    </div>

    <div id="session-detail-modal" class="modal">
        <div class="modal-content">
            <h3>Chi tiết của Session</h3>
            <div class="summary-boxes">
                <div class="summary-box">
                    <p>Tổng số bản tin Consume</p>
                    <p><strong id="consume-count">-</strong></p>
                </div>
                <div class="summary-box">
                    <p>Tổng số bản tin Process</p>
                     <p><strong id="process-count">-</strong></p>
                </div>
                <div class="summary-box">
                    <p>Tổng số bản tin Error</p>
                     <p><strong id="error-count">-</strong></p>
                </div>
            </div>
            <table id="kafka-topics-table">
                <thead>
                    <tr>
                        <th>Tên Topic</th>
                        <th>Lag</th>
                    </tr>
                </thead>
                <tbody id="kafka-topics-body">
                    <!-- Kafka topics data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load initial data when page loads
            loadSessions();

            const logSyncBtn = document.getElementById('log-sync-btn');
            const sessionListView = document.getElementById('session-list-view');
            const errorLogView = document.getElementById('error-log-view');
            const modal = document.getElementById('session-detail-modal');

            // Navigation Logic - Switch to Error Log view when "Log Sync" button is clicked
            logSyncBtn.addEventListener('click', () => {
                sessionListView.style.display = 'none';
                errorLogView.style.display = 'block';
                loadErrorLogs(); // Load error logs when switching
            });

            // Modal Logic - Close the modal if the user clicks on the dim background
            modal.addEventListener('click', (event) => {
                if (event.target === modal) {
                    closeModal();
                }
            });

            // Input change events for live filtering
            document.getElementById('connector-id').addEventListener('input', function() {
                if (sessionListView.style.display !== 'none') {
                    loadSessions();
                }
            });

            document.getElementById('session-id').addEventListener('input', function() {
                if (sessionListView.style.display !== 'none') {
                    loadSessions();
                }
            });
        });

        function loadSessions() {
            const connectorId = document.getElementById('connector-id').value;
            const sessionId = document.getElementById('session-id').value;
            
            const params = new URLSearchParams({
                page: 1,
                limit: 10
            });
            
            if (connectorId) params.append('connector_id', connectorId);
            if (sessionId) params.append('session_id', sessionId);

            // Show loading spinner
            document.getElementById('sessions-loading').style.display = 'block';
            document.getElementById('sessions-table').style.display = 'none';
            
            fetch(`/api/sessions?${params}`)
                .then(response => response.json())
                .then(data => {
                    renderSessionsTable(data.sessions);
                    // Hide loading spinner
                    document.getElementById('sessions-loading').style.display = 'none';
                    document.getElementById('sessions-table').style.display = 'table';
                })
                .catch(error => {
                    console.error('Error loading sessions:', error);
                    document.getElementById('sessions-loading').style.display = 'none';
                    document.getElementById('sessions-table').style.display = 'table';
                });
        }

        function renderSessionsTable(sessions) {
            const tbody = document.getElementById('sessions-table-body');
            tbody.innerHTML = '';
            
            if (!sessions || sessions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center;">No sessions found</td></tr>';
                return;
            }
            
            sessions.forEach(session => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${session.id}</td>
                    <td>${session.session_id}</td>
                    <td>${session.start_time}</td>
                    <td>${session.end_time}</td>
                    <td><span class="badge ${session.consumer_status}">${session.consumer_status}</span></td>
                    <td><span class="badge ${session.process_status}">${session.process_status}</span></td>
                    <td>${session.mode}</td>
                    <td><button class="action-button" onclick="showSessionDetail('${session.session_id}')">Chi tiết</button></td>
                `;
                tbody.appendChild(row);
            });
        }

        function loadErrorLogs() {
            // Show loading spinner
            document.getElementById('errors-loading').style.display = 'block';
            document.getElementById('errors-table').style.display = 'none';

            // For now, use mock data - in real implementation, this would fetch from API
            const mockErrors = [
                {
                    type: "Khởi tạo Pipeline",
                    action_time: "2025-09-08T12:04:03+07:00",
                    error_message: "HTTPConnectionPool(host='dataorchestration-api-service.mobio', port=80): Max retries exceeded with url: /orchestration/api/v1.0/pipeline_config/d0ed51479adf... (Caused by ResponseError('too many 400 error responses'))",
                    curl: "curl -X POST 'http://dataorchestration-api-service.mobio/orchestration/api/v1.0/pipeline_config/d0ed51479adf' -H 'Content-Type: application/json' -d '{...}'"
                }
            ];

            setTimeout(() => {
                renderErrorLogsTable(mockErrors);
                document.getElementById('errors-loading').style.display = 'none';
                document.getElementById('errors-table').style.display = 'table';
            }, 500);
        }

        function renderErrorLogsTable(errors) {
            const tbody = document.getElementById('errors-table-body');
            tbody.innerHTML = '';
            
            if (!errors || errors.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;">No error logs found</td></tr>';
                return;
            }
            
            errors.forEach(error => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${error.type}</td>
                    <td>${error.action_time}</td>
                    <td style="max-width: 300px; word-wrap: break-word;">${error.error_message}</td>
                    <td><button class="copy-button" onclick="copyCurl('${error.curl}')">Copy</button></td>
                `;
                tbody.appendChild(row);
            });
        }

        function showSessionDetail(sessionId) {
            // Fetch session detail from API
            fetch(`/api/sessions/${sessionId}/detail`)
                .then(response => response.json())
                .then(data => {
                    // Update modal content
                    document.getElementById('consume-count').textContent = data.consumer_count;
                    document.getElementById('process-count').textContent = data.process_count;
                    document.getElementById('error-count').textContent = data.error_count;
                    
                    renderKafkaTopicsTable(data.kafka_topics);
                    openModal();
                })
                .catch(error => {
                    console.error('Error loading session detail:', error);
                    alert('Error loading session details');
                });
        }

        function renderKafkaTopicsTable(topics) {
            const tbody = document.getElementById('kafka-topics-body');
            tbody.innerHTML = '';
            
            if (!topics || topics.length === 0) {
                tbody.innerHTML = '<tr><td colspan="2" style="text-align: center;">No Kafka topics found</td></tr>';
                return;
            }
            
            topics.forEach(topic => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${topic.topic_name}</td>
                    <td>${topic.lag}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function goBackToSessions() {
            document.getElementById('error-log-view').style.display = 'none';
            document.getElementById('session-list-view').style.display = 'block';
        }

        function copyCurl(curlCommand) {
            navigator.clipboard.writeText(curlCommand).then(() => {
                alert('cURL command copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy: ', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = curlCommand;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('cURL command copied to clipboard!');
            });
        }

        // Modal functions
        function openModal() {
            document.getElementById('session-detail-modal').style.display = 'flex';
        }

        function closeModal() {
            document.getElementById('session-detail-modal').style.display = 'none';
        }
    </script>

</body>
</html>
