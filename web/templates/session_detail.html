<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Detail - AM Monitor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .main-content {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
        .btn-action {
            padding: 4px 12px;
            font-size: 0.875rem;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #28a745;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar mb-4">
                    <h5 class="mb-3">Thông tin</h5>
                    <form>
                        <div class="mb-3">
                            <label for="connectorId" class="form-label">ConnectorID</label>
                            <input type="text" class="form-control" id="connectorId" name="connector_id">
                        </div>
                        <div class="mb-3">
                            <label for="sessionId" class="form-label">SessionID</label>
                            <input type="text" class="form-control" id="sessionId" name="session_id" readonly>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="loadSessionDetail()">Monitor</button>
                    </form>
                </div>

                <div class="sidebar">
                    <h5 class="mb-3">Thông tin chi tiết Connector</h5>
                    <div class="mb-3">
                        <label for="connectorName" class="form-label">Tên:</label>
                        <input type="text" class="form-control" id="connectorName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Trạng thái kết nối:</label>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="mb-3">
                        <label for="connectorStatus" class="form-label">Trạng thái đồng bộ:</label>
                        <input type="text" class="form-control" id="connectorStatus" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="jobName" class="form-label">Loại Connector:</label>
                        <input type="text" class="form-control" id="jobName" value="orchestration-data-pipeline-report" readonly>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">
                            Job công<br>
                            hợp báo cáo
                        </small>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="button" class="btn btn-success w-100" onclick="goToDashboard()">
                        Log Sync AM -> Orches
                    </button>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="main-content">
                    <!-- Session Info Table -->
                    <div class="table-responsive mb-4">
                        <table class="table table-striped" id="sessionInfoTable">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>SessionID</th>
                                    <th>Thời gian bắt đầu</th>
                                    <th>Thời gian kết thúc</th>
                                    <th>Consumer Status</th>
                                </tr>
                            </thead>
                            <tbody id="sessionInfoTableBody">
                                <!-- Session info will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Session Detail Panel -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Chi tiết của Session</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <button class="btn btn-info w-100" id="consumerBtn" onclick="showConsumerCount()">
                                        <span id="consumerCount">Tổng số bản tin Consumer</span>
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-warning w-100" id="processBtn" onclick="showProcessCount()">
                                        <span id="processCount">Tổng số bản tin Process</span>
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-danger w-100" id="errorBtn" onclick="showErrorCount()">
                                        <span id="errorCount">Tổng số bản tin Error</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Kafka Topics Table -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Kafka Topics</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Tên Topic</th>
                                            <th>Lag</th>
                                        </tr>
                                    </thead>
                                    <tbody id="kafkaTopicsTableBody">
                                        <!-- Kafka topics will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
    <script>
    let sessionDetailData = null;

    // Load session detail when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Get session ID from URL path
        const path = window.location.pathname;
        const sessionId = path.split('/')[2]; // /sessions/{session_id}
        
        if (sessionId) {
            document.getElementById('sessionId').value = sessionId;
            loadSessionDetail();
        }
    });

    function loadSessionDetail() {
        const sessionId = document.getElementById('sessionId').value || getSessionIdFromURL();
        
        if (!sessionId) {
            console.error('No session ID found');
            return;
        }
        
        fetch(`/api/sessions/${sessionId}/detail`)
            .then(response => response.json())
            .then(data => {
                sessionDetailData = data;
                renderSessionInfo(data.session_info);
                renderKafkaTopics(data.kafka_topics);
                updateCountButtons(data);
            })
            .catch(error => {
                console.error('Error loading session detail:', error);
            });
    }

    function getSessionIdFromURL() {
        const path = window.location.pathname;
        return path.split('/')[2]; // /sessions/{session_id}
    }

    function renderSessionInfo(sessionInfo) {
        const tbody = document.getElementById('sessionInfoTableBody');
        tbody.innerHTML = '';
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${sessionInfo.id}</td>
            <td>${sessionInfo.session_id}</td>
            <td>${sessionInfo.start_time}</td>
            <td>${sessionInfo.end_time}</td>
            <td><span class="badge ${getStatusBadgeClass(sessionInfo.consumer_status)}">${sessionInfo.consumer_status}</span></td>
        `;
        tbody.appendChild(row);
    }

    function renderKafkaTopics(kafkaTopics) {
        const tbody = document.getElementById('kafkaTopicsTableBody');
        tbody.innerHTML = '';
        
        kafkaTopics.forEach(topic => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${topic.topic_name}</td>
                <td><span class="badge bg-info">${topic.lag}</span></td>
            `;
            tbody.appendChild(row);
        });
    }

    function updateCountButtons(data) {
        document.getElementById('consumerCount').textContent = `Tổng số bản tin Consumer: ${data.consumer_count}`;
        document.getElementById('processCount').textContent = `Tổng số bản tin Process: ${data.process_count}`;
        document.getElementById('errorCount').textContent = `Tổng số bản tin Error: ${data.error_count}`;
    }

    function showConsumerCount() {
        if (sessionDetailData) {
            alert(`Tổng số bản tin Consumer: ${sessionDetailData.consumer_count}`);
        }
    }

    function showProcessCount() {
        if (sessionDetailData) {
            alert(`Tổng số bản tin Process: ${sessionDetailData.process_count}`);
        }
    }

    function showErrorCount() {
        if (sessionDetailData) {
            alert(`Tổng số bản tin Error: ${sessionDetailData.error_count}`);
        }
    }

    function getStatusBadgeClass(status) {
        switch (status) {
            case 'finished': return 'bg-success';
            case 'running': case 'processing': return 'bg-warning';
            case 'failed': case 'error': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    function goToDashboard() {
        window.location.href = '/';
    }
    </script>
</body>
</html>