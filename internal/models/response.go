package models

// Màn 1 - Dashboard Response
type SessionResponse struct {
	ID             int    `json:"id"`
	SessionID      string `json:"session_id"`
	StartTime      string `json:"start_time"`       // "2025-09-10 12:30"
	EndTime        string `json:"end_time"`         // "2025-09-11 00:15"
	ConsumerStatus string `json:"consumer_status"`  // "finished"
	ProcessStatus  string `json:"process_status"`   // "finished"
	Mode           string `json:"mode"`             // "Batch"
}

type SessionListResponse struct {
	Sessions []SessionResponse `json:"sessions"`
	Total    int               `json:"total"`
}

// Màn 2 - Error Logs Response
type ErrorLogResponse struct {
	Type         string `json:"type"`          // "Khởi tạo Pipeline"
	ActionTime   string `json:"action_time"`   // "2025-09-NET12.04:03+07:00"
	ErrorMessage string `json:"error_message"`
	CURL         string `json:"curl"`
}

type ErrorLogListResponse struct {
	Title     string             `json:"title"` // "Danh sách request lỗi khi sync data từ Marketplace sang Orchestration"
	ErrorLogs []ErrorLogResponse `json:"error_logs"`
}

// Màn 3 - Session Detail Response
type KafkaTopicResponse struct {
	TopicName string `json:"topic_name"` // "am.process"
	Lag       int    `json:"lag"`        // 1000
}

type SessionDetailResponse struct {
	SessionInfo   SessionResponse      `json:"session_info"`
	ConsumerCount int                  `json:"consumer_count"`
	ProcessCount  int                  `json:"process_count"`
	ErrorCount    int                  `json:"error_count"`
	KafkaTopics   []KafkaTopicResponse `json:"kafka_topics"`
}

// Request models cho filters
type SessionFilter struct {
	ConnectorID string `form:"connector_id"`
	SessionID   string `form:"session_id"`
	Page        int    `form:"page"`
	Limit       int    `form:"limit"`
}