package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// GET / - Màn 1 Dashboard
func Dashboard(c *gin.Context) {
	c.HTML(http.StatusOK, "dashboard.html", gin.H{
		"title": "AM Monitor Dashboard",
	})
}

// GET /errors - Màn 2 Error Logs
func ErrorLogs(c *gin.Context) {
	c.HTML(http.StatusOK, "error_logs.html", gin.H{
		"title": "Error Logs",
	})
}

// GET /sessions/{session_id} - Màn 3 Session Detail
func SessionDetail(c *gin.Context) {
	sessionID := c.Param("session_id")
	c.HTML(http.StatusOK, "session_detail.html", gin.H{
		"title":      "Session Detail",
		"session_id": sessionID,
	})
}