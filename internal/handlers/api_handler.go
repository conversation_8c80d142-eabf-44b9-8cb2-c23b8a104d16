package handlers

import (
	"net/http"

	"am-monitor/internal/models"

	"github.com/gin-gonic/gin"
)

// GET /api/sessions?connector_id=&session_id=&page=1&limit=10
func GetSessions(c *gin.Context) {
	var filter models.SessionFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set defaults
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.Limit <= 0 {
		filter.Limit = 10
	}

	// Mock data - bạn sẽ thay thế logic này
	mockSessions := []models.SessionResponse{
		{
			ID:             3707,
			SessionID:      "250910053049147",
			StartTime:      "2025-09-10 12:30",
			EndTime:        "2025-09-11 00:15",
			ConsumerStatus: "finished",
			ProcessStatus:  "finished",
			Mode:           "Batch",
		},
		{
			ID:             3708,
			SessionID:      "250910053049148",
			StartTime:      "2025-09-11 08:00",
			EndTime:        "2025-09-11 16:30",
			ConsumerStatus: "running",
			ProcessStatus:  "processing",
			Mode:           "Streaming",
		},
		{
			ID:             3709,
			SessionID:      "250910053049149",
			StartTime:      "2025-09-11 14:15",
			EndTime:        "",
			ConsumerStatus: "failed",
			ProcessStatus:  "error",
			Mode:           "Batch",
		},
	}

	// Apply filtering (mock implementation)
	filteredSessions := mockSessions
	if filter.ConnectorID != "" || filter.SessionID != "" {
		// TODO: Apply actual filtering logic here
	}

	response := models.SessionListResponse{
		Sessions: filteredSessions,
		Total:    len(filteredSessions),
	}

	c.JSON(http.StatusOK, response)
}

// GET /api/sessions/{session_id}/errors
func GetSessionErrors(c *gin.Context) {
	sessionID := c.Param("session_id")

	// Mock data
	mockErrors := []models.ErrorLogResponse{
		{
			Type:         "Khởi tạo Pipeline",
			ActionTime:   "2025-09-NET12.04:03+07:00",
			ErrorMessage: "HTTPConnectionPool(host='dataorchestration-api-service.mobio', port=80): Max retries exceeded with url: /orchestration/api/v1/pipeline_config/6db514d75bdfd3a6a6aaf4/start (Caused by ResponseErr('too many 400 error responses'))",
			CURL:         "curl -X POST 'https://dataorchestration-api-service.mobio/orchestration/api/v1/pipeline_config/6db514d75bdfd3a6a6aaf4/start' -H 'Content-Type: application/json' -d '{\"session_id\":\"" + sessionID + "\"}'",
		},
		{
			Type:         "Data Processing",
			ActionTime:   "2025-09-NET12.05:15+07:00",
			ErrorMessage: "Connection timeout after 30s",
			CURL:         "curl -X GET 'https://api.marketplace.mobio/data/sync' -H 'Authorization: Bearer token123'",
		},
	}

	response := models.ErrorLogListResponse{
		Title:     "Danh sách request lỗi khi sync data từ Marketplace sang Orchestration",
		ErrorLogs: mockErrors,
	}

	c.JSON(http.StatusOK, response)
}

// GET /api/sessions/{session_id}/detail
func GetSessionDetail(c *gin.Context) {
	sessionID := c.Param("session_id")

	// Mock data
	sessionInfo := models.SessionResponse{
		ID:             3707,
		SessionID:      sessionID,
		StartTime:      "2025-09-10 12:30",
		EndTime:        "2025-09-11 00:15",
		ConsumerStatus: "finished",
		ProcessStatus:  "finished",
		Mode:           "Batch",
	}

	mockKafkaTopics := []models.KafkaTopicResponse{
		{
			TopicName: "am.process",
			Lag:       1000,
		},
		{
			TopicName: "am.report",
			Lag:       100,
		},
		{
			TopicName: "orchestration.events",
			Lag:       50,
		},
	}

	response := models.SessionDetailResponse{
		SessionInfo:   sessionInfo,
		ConsumerCount: 150,
		ProcessCount:  98,
		ErrorCount:    5,
		KafkaTopics:   mockKafkaTopics,
	}

	c.JSON(http.StatusOK, response)
}

// POST /api/sessions/sync-trigger
func TriggerSync(c *gin.Context) {
	// Mock implementation
	c.JSON(http.StatusOK, gin.H{
		"message": "Sync operation triggered successfully",
		"status":  "initiated",
	})
}