package config

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

type Config struct {
	KafkaBroker string
	MongoDBURI  string
	StarRockURI string
	ServerPort  string
}

func Load() *Config {
	// Load .env file
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	return &Config{
		KafkaBroker: getEnv("KAFKA_BROKER", "localhost:9092"),
		MongoDBURI:  getEnv("MONGODB_URI", "mongodb://localhost:27017/am_monitor"),
		StarRockURI: getEnv("STARROCK_URI", "starrocks://localhost:9030/analytics"),
		ServerPort:  getEnv("SERVER_PORT", "8081"),
	}
}

func getEnv(key, fallback string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return fallback
}