package server

import (
	"am-monitor/internal/config"
	"am-monitor/internal/handlers"

	"github.com/gin-gonic/gin"
)

func Setup(cfg *config.Config) *gin.Engine {
	// Set Gin mode to debug for better error messages
	gin.SetMode(gin.DebugMode)
	
	r := gin.Default()
	
	// Load HTML templates
	r.LoadHTMLGlob("web/templates/*")
	
	// Static files
	r.Static("/static", "web/static")
	
	// Web routes (HTML pages)
	r.GET("/", handlers.Dashboard)
	r.GET("/errors", handlers.ErrorLogs)
	r.GET("/sessions/:session_id", handlers.SessionDetail)
	
	// API routes
	api := r.Group("/api")
	{
		sessions := api.Group("/sessions")
		{
			sessions.GET("", handlers.GetSessions)
			sessions.GET("/:session_id/errors", handlers.GetSessionErrors)
			sessions.GET("/:session_id/detail", handlers.GetSessionDetail)
			sessions.POST("/sync-trigger", handlers.TriggerSync)
		}
	}
	
	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "OK",
			"service": "AM Monitor",
			"version": "1.0.0",
		})
	})
	
	return r
}