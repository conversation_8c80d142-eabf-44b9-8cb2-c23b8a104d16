package main

import (
	"log"

	"am-monitor/internal/config"
	"am-monitor/internal/server"
)

func main() {
	// Load configuration
	cfg := config.Load()
	
	log.Println("Starting AM Monitor server...")
	log.Printf("Configuration loaded - MongoDB: %s, Kafka: %s", 
		cfg.MongoDBURI, cfg.KafkaBroker)
	
	// Setup server
	r := server.Setup(cfg)
	
	// Start server
	serverAddr := ":" + cfg.ServerPort
	log.Printf("Server running on http://localhost%s", serverAddr)
	log.Println("Dashboard available at: http://localhost" + serverAddr + "/")
	log.Println("API documentation: http://localhost" + serverAddr + "/health")
	
	if err := r.Run(serverAddr); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}