# Tài liệu Yêu cầ<PERSON> phẩm (PRD): Data Pipeline Monitoring & Operations Dashboard

---

## 1. Tổng quan & Tầm nhìn

**Sản phẩm:** Một trang web nội bộ (internal web application) cung cấp giao diện tập trung để giám sát, gỡ lỗi và thực hiện các thao tác cơ bản trên hệ thống pipeline đồng bộ dữ liệu giữa các microservices (ví dụ: từ Marketplace sang Orchestration).

**Vấn đề cần giải quyết:**
* **Thiếu khả năng quan sát (Observability):** Hiện tại, việc kiểm tra trạng thái các pipeline đòi hỏi truy cập (SSH) vào nhiều máy chủ, đọc logs thủ công, và sử dụng nhiều công cụ dòng lệnh khác nhau. <PERSON>u<PERSON> trình n<PERSON> chậ<PERSON>, phân mảnh và không phải kỹ sư nào cũng có đủ quyền truy cập.
* **Thời gian khắc phục sự cố (MTTR) cao:** Khi một luồng dữ liệu bị lỗi, việc xác định nguyên nhân gốc rễ (root cause) rất tốn thời gian. Kỹ sư phải tổng hợp thông tin từ nhiều nguồn để hiểu được bối cảnh của lỗi.
* **Rủi ro vận hành:** Việc kích hoạt lại một luồng đồng bộ dữ liệu lớn yêu cầu các câu lệnh phức tạp, tiềm ẩn nguy cơ sai sót do con người.

**Tầm nhìn:** Xây dựng một "trung tâm chỉ huy" tin cậy, trực quan và an toàn cho toàn bộ hệ thống data pipeline, giúp đội ngũ kỹ sư chuyển từ trạng thái "phản ứng" (reactive) sang "chủ động" (proactive) trong việc vận hành hệ thống.

---

## 2. Đối tượng Người dùng

1.  **Kỹ sư Vận hành (DevOps/SRE):** Cần một cái nhìn tổng quan về sức khỏe hệ thống, nhanh chóng xác định các điểm bất thường (anomaly) và các "điểm nghẽn cổ chai" (bottleneck).
2.  **Kỹ sư Dữ liệu (Data Engineers):** Cần theo dõi chi tiết các phiên đồng bộ, số lượng bản ghi đã xử lý, và tình trạng của các message queue (Kafka topics).
3.  **Kỹ sư Backend:** Cần gỡ lỗi các request API thất bại, xem chi tiết lỗi và có khả năng tái tạo lỗi một cách nhanh chóng.

---

## 3. Mục tiêu & Chỉ số Thành công (OKRs)

* **Mục tiêu 1: Tăng cường khả năng quan sát toàn diện hệ thống data pipeline.**
    * **KR1:** 100% các phiên đồng bộ dữ liệu quan trọng được hiển thị trạng thái (thành công, thất bại, đang chạy) trên dashboard trong vòng 30 giây kể từ khi trạng thái thay đổi.
    * **KR2:** Giảm 90% số lần kỹ sư cần phải SSH vào máy chủ để kiểm tra trạng thái pipeline cơ bản.
* **Mục tiêu 2: Rút ngắn thời gian chẩn đoán và khắc phục sự cố.**
    * **KR1:** Giảm 50% thời gian trung bình để khắc phục sự cố (MTTR) đối với các lỗi đồng bộ dữ liệu.
    * **KR2:** 100% các lỗi API được ghi nhận trên dashboard đều đi kèm với cURL command để có thể tái tạo lỗi ngay lập tức.
* **Mục tiêu 3: Đơn giản hóa và bảo mật hóa các tác vụ vận hành.**
    * **KR1:** Cung cấp chức năng "Kích hoạt lại toàn bộ" (Sync All) qua giao diện, thay thế 100% các kịch bản (scripts) thủ công phức tạp.
    * **KR2:** Tích hợp xác thực và phân quyền, đảm bảo chỉ những người dùng được ủy quyền mới có thể thực hiện các tác vụ vận hành.

---

## 4. Yêu cầu Chức năng (Functional Requirements)

Dựa trên 3 giao diện được cung cấp, các chức năng được chia thành các User Story sau:

**Epic: Giám sát Phiên đồng bộ (Sync Session Monitoring)**

* **Story 1 - Màn hình chính (Dashboard):**
    * **As a** DevOps Engineer,
    * **I want to** xem danh sách tất cả các phiên đồng bộ gần nhất,
    * **So that I can** có một cái nhìn tổng quan về hoạt động của hệ thống.
    * **Acceptance Criteria:**
        * Hiển thị bảng danh sách các phiên với các cột: ID, SessionID, Thời gian bắt đầu, Thời gian kết thúc, Trạng thái Consume, Trạng thái Process, Mode (Batch/Real-time), và nút hành động "Chi tiết".
        * Có bộ lọc theo `ConnectorID` và `SessionID`.
        * Trạng thái được mã hóa bằng màu sắc để dễ nhận biết (ví dụ: Xanh - thành công, Đỏ - thất bại, Vàng - đang chạy).

* **Story 2 - Màn hình chi tiết Phiên:**
    * **As a** Data Engineer,
    * **I want to** xem chi tiết một phiên đồng bộ cụ thể,
    * **So that I can** phân tích hiệu suất và kiểm tra các chỉ số về message queue.
    * **Acceptance Criteria:**
        * Hiển thị các chỉ số tổng quan của phiên: Tổng số bản tin Consume, Process, Error.
        * Hiển thị bảng trạng thái các Kafka Topic liên quan với các cột: Tên Topic, Độ trễ (Lag).
        * Dữ liệu được làm mới tự động mỗi 30 giây.

**Epic: Gỡ lỗi Pipeline (Pipeline Debugging)**

* **Story 3 - Màn hình danh sách lỗi API:**
    * **As a** Backend Engineer,
    * **I want to** xem danh sách các request API bị lỗi trong một phiên,
    * **So that I can** nhanh chóng xác định nguyên nhân và tái tạo lỗi.
    * **Acceptance Criteria:**
        * Hiển thị bảng danh sách các lỗi với các cột: Loại lỗi, Thời gian, Nội dung lỗi (Error Message), URL, cURL.
        * Nội dung lỗi phải chi tiết, rõ ràng (ví dụ: `Max retries exceeded`, `too many 400 error responses`).
        * Cung cấp nút "Copy" cho cURL command.

**Epic: Vận hành Pipeline (Pipeline Operations)**

* **Story 4 - Kích hoạt đồng bộ lại:**
    * **As an** authorized Operator,
    * **I want to** kích hoạt một luồng đồng bộ lại toàn bộ dữ liệu từ Marketplace sang Orchestration chỉ bằng một cú nhấp chuột,
    * **So that I can** thực hiện tác vụ vận hành một cách nhanh chóng và an toàn.
    * **Acceptance Criteria:**
        * Có một nút "Log Sync All -> Orches" trên giao diện.
        * Khi nhấn nút, phải có một hộp thoại xác nhận (confirmation dialog) để tránh thao tác nhầm.
        * Chức năng này phải được bảo vệ bằng quyền truy cập.

---

## 5. Yêu cầu Phi chức năng (Non-Functional Requirements)

* **Hiệu năng (Performance):**
    * Thời gian tải trang lần đầu < 2 giây.
    * Thời gian phản hồi của các API backend < 200ms ở percentile thứ 95.
    * Dữ liệu trên dashboard phải được làm mới tự động (qua WebSockets hoặc polling) với độ trễ không quá 10 giây so với thời gian thực.
* **Bảo mật (Security):**
    * Hệ thống phải được tích hợp với hệ thống xác thực tập trung của công ty (ví dụ: LDAP, OAuth 2.0). Sử dụng **JWT** để xác thực các request API từ frontend.
    * Phân quyền truy cập rõ ràng:
        * **Viewer:** Chỉ có quyền xem.
        * **Operator:** Có quyền xem và thực hiện các tác vụ vận hành (ví dụ: kích hoạt sync).
* **Khả năng mở rộng (Scalability):**
    * Hệ thống phải có khả năng xử lý và hiển thị dữ liệu cho ít nhất 100 connectors và 10,000 phiên đồng bộ mỗi ngày mà không bị suy giảm hiệu năng.
* **Độ tin cậy (Reliability):**
    * Uptime của hệ thống phải đạt 99.9%.

---

## 6. Ngoài phạm vi (Out of Scope)

* **MVP (Minimum Viable Product) này sẽ không bao gồm:**
    * Hệ thống cảnh báo (alerting) qua email/slack.
    * Phân tích xu hướng và hiệu năng trong lịch sử dài hạn.
    * Khả năng cấu hình connectors trực tiếp từ giao diện.

---

## Cấu trúc Project Golang & Kiến trúc Hệ thống

Để đáp ứng các yêu cầu trên, tôi đề xuất một cấu trúc dự án Golang theo kiến trúc phân lớp (Layered Architecture), tách biệt rõ ràng giữa giao diện, logic nghiệp vụ và truy cập dữ liệu. Điều này giúp dự án dễ bảo trì, mở rộng và kiểm thử.

### 1. Sơ đồ Luồng Request

1.  **User -> Browser:** Người dùng truy cập vào URL của trang Monitor.
2.  **Browser -> Go Server (Web Handler):** Server Go nhận request, xử lý và trả về một file HTML tĩnh.
3.  **Browser (JavaScript) -> Go Server (API Handler):** Sau khi trang HTML được tải, JavaScript trên trình duyệt sẽ thực hiện các lời gọi AJAX/Fetch đến các API endpoint (`/api/...`) của chính server Go đó.
4.  **Go Server (API Handler) -> Service Layer -> Repository Layer:**
    * **API Handler:** Phân tích request, gọi đến lớp **Service**.
    * **Service:** Chứa logic nghiệp vụ (ví dụ: tổng hợp dữ liệu từ nhiều nguồn). Gọi đến lớp **Repository**.
    * **Repository:** Tương tác trực tiếp với các nguồn dữ liệu (database, Kafka Admin Client, logs,...) để lấy dữ liệu thô.
5.  **Go Server -> Browser:** Dữ liệu được trả về dưới dạng JSON, và JavaScript sẽ cập nhật giao diện người dùng (DOM).

### 2. Cấu trúc Thư mục Dự án

```plaintext
/monitor-dashboard
├── cmd/
│   └── web/
│       └── main.go              # Điểm khởi chạy của ứng dụng
├── internal/
│   ├── api/                     # Xử lý các request API (/api/v1/...)
│   │   ├── handlers.go          # Các API handlers (ví dụ: GetSessions, GetSessionDetails)
│   │   └── router.go            # Định tuyến các API endpoint
│   ├── web/                     # Xử lý các request phục vụ trang web
│   │   ├── handlers.go          # Các web handlers (ví dụ: ServeDashboardPage)
│   │   └── router.go            # Định tuyến các trang HTML
│   ├── server/
│   │   └── server.go            # Khởi tạo và cấu hình HTTP server, gắn các router
│   ├── service/                 # Tầng logic nghiệp vụ
│   │   ├── monitor_service.go   # Logic chính: lấy dữ liệu, tổng hợp
│   │   └── operation_service.go # Logic cho các tác vụ vận hành (trigger sync)
│   ├── repository/              # Tầng truy cập dữ liệu
│   │   ├── db_repo.go           # Truy vấn database (PostgreSQL/MySQL)
│   │   └── kafka_repo.go        # Tương tác với Kafka (lấy lag,...)
│   └── models/                  # Định nghĩa các cấu trúc dữ liệu (structs)
│       ├── connector.go
│       ├── session.go
│       └── error_log.go
├── web/                         # Chứa các tài sản của frontend
│   ├── templates/               # Các file HTML template
│   │   ├── layouts/
│   │   │   └── base.html
│   │   ├── dashboard.html
│   │   ├── session_detail.html
│   │   └── error_log.html
│   └── static/                  # Các file CSS, JS, ảnh
│       ├── css/
│       │   └── style.css
│       └── js/
│           └── app.js           # Logic JS của frontend để gọi API và cập nhật UI
├── go.mod
├── go.sum
└── config.yaml                  # File cấu hình (database connection, kafka brokers,...)